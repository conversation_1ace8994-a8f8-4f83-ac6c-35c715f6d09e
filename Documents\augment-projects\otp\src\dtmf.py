"""
DTMF (Dual-Tone Multi-Frequency) Signal Generation and Detection

This module implements educational DTMF tone generation and detection
for simulating telephone keypad presses.

DTMF uses two simultaneous tones to represent each key:
- Low frequency group: 697, 770, 852, 941 Hz
- High frequency group: 1209, 1336, 1477, 1633 Hz
"""

import numpy as np
import scipy.signal
from typing import Optional, Tuple, List
import matplotlib.pyplot as plt


class DTMFGenerator:
    """Generate DTMF tones for telephone keypad simulation"""
    
    # DTMF frequency mapping (ITU-T Recommendation Q.23)
    DTMF_FREQUENCIES = {
        '1': (697, 1209), '2': (697, 1336), '3': (697, 1477), 'A': (697, 1633),
        '4': (770, 1209), '5': (770, 1336), '6': (770, 1477), 'B': (770, 1633),
        '7': (852, 1209), '8': (852, 1336), '9': (852, 1477), 'C': (852, 1633),
        '*': (941, 1209), '0': (941, 1336), '#': (941, 1477), 'D': (941, 1633)
    }
    
    def __init__(self, sample_rate: int = 8000):
        """
        Initialize DTMF generator
        
        Args:
            sample_rate: Audio sample rate in Hz (typical: 8000 for telephony)
        """
        self.sample_rate = sample_rate
    
    def generate_tone(self, key: str, duration: float = 0.2) -> np.ndarray:
        """
        Generate DTMF tone for a specific key
        
        Args:
            key: Key to generate tone for ('0'-'9', '*', '#', 'A'-'D')
            duration: Duration of tone in seconds
            
        Returns:
            numpy array containing the audio signal
        """
        if key.upper() not in self.DTMF_FREQUENCIES:
            raise ValueError(f"Invalid DTMF key: {key}")
        
        low_freq, high_freq = self.DTMF_FREQUENCIES[key.upper()]
        
        # Generate time array
        t = np.linspace(0, duration, int(self.sample_rate * duration), False)
        
        # Generate dual tones with equal amplitude
        low_tone = np.sin(2 * np.pi * low_freq * t)
        high_tone = np.sin(2 * np.pi * high_freq * t)
        
        # Combine tones (normalize to prevent clipping)
        signal = (low_tone + high_tone) * 0.5
        
        # Apply envelope to reduce clicking
        envelope = self._create_envelope(len(signal))
        signal *= envelope
        
        return signal
    
    def generate_sequence(self, keys: str, tone_duration: float = 0.2, 
                         pause_duration: float = 0.1) -> np.ndarray:
        """
        Generate a sequence of DTMF tones
        
        Args:
            keys: String of keys to generate (e.g., "123*0#")
            tone_duration: Duration of each tone in seconds
            pause_duration: Pause between tones in seconds
            
        Returns:
            numpy array containing the complete sequence
        """
        signals = []
        pause_samples = int(self.sample_rate * pause_duration)
        pause = np.zeros(pause_samples)
        
        for i, key in enumerate(keys):
            if key == ' ':
                # Extended pause for space
                signals.append(np.zeros(pause_samples * 3))
            else:
                signals.append(self.generate_tone(key, tone_duration))
                if i < len(keys) - 1:  # No pause after last tone
                    signals.append(pause)
        
        return np.concatenate(signals)
    
    def _create_envelope(self, length: int, fade_samples: int = None) -> np.ndarray:
        """Create envelope to reduce clicking artifacts"""
        if fade_samples is None:
            fade_samples = min(length // 20, int(self.sample_rate * 0.01))  # 1% or 10ms
        
        envelope = np.ones(length)
        
        # Fade in
        envelope[:fade_samples] = np.linspace(0, 1, fade_samples)
        # Fade out
        envelope[-fade_samples:] = np.linspace(1, 0, fade_samples)
        
        return envelope


class DTMFDetector:
    """Detect DTMF tones from audio signals"""
    
    def __init__(self, sample_rate: int = 8000, window_size: float = 0.05):
        """
        Initialize DTMF detector
        
        Args:
            sample_rate: Audio sample rate in Hz
            window_size: Analysis window size in seconds
        """
        self.sample_rate = sample_rate
        self.window_size = window_size
        self.window_samples = int(sample_rate * window_size)
        
        # DTMF frequencies for detection
        self.low_freqs = [697, 770, 852, 941]
        self.high_freqs = [1209, 1336, 1477, 1633]
        
        # Reverse mapping for detection
        self.freq_to_key = {}
        for key, (low, high) in DTMFGenerator.DTMF_FREQUENCIES.items():
            self.freq_to_key[(low, high)] = key
    
    def detect_tone(self, signal: np.ndarray, threshold: float = 0.1) -> Optional[str]:
        """
        Detect DTMF tone in audio signal
        
        Args:
            signal: Audio signal to analyze
            threshold: Minimum energy threshold for detection
            
        Returns:
            Detected key or None if no valid tone found
        """
        if len(signal) < self.window_samples:
            return None
        
        # Use middle portion of signal for analysis
        start = len(signal) // 4
        end = 3 * len(signal) // 4
        analysis_signal = signal[start:end]
        
        # Compute FFT
        fft = np.fft.fft(analysis_signal)
        freqs = np.fft.fftfreq(len(analysis_signal), 1/self.sample_rate)
        magnitude = np.abs(fft)
        
        # Find peaks at DTMF frequencies
        detected_low = self._find_peak_frequency(freqs, magnitude, self.low_freqs, threshold)
        detected_high = self._find_peak_frequency(freqs, magnitude, self.high_freqs, threshold)
        
        if detected_low and detected_high:
            return self.freq_to_key.get((detected_low, detected_high))
        
        return None
    
    def _find_peak_frequency(self, freqs: np.ndarray, magnitude: np.ndarray, 
                           target_freqs: List[int], threshold: float) -> Optional[int]:
        """Find the strongest frequency component near target frequencies"""
        best_freq = None
        best_magnitude = 0
        
        for target_freq in target_freqs:
            # Find frequency bin closest to target
            freq_idx = np.argmin(np.abs(freqs - target_freq))
            
            # Check magnitude in a small window around the target frequency
            window = 5  # bins
            start_idx = max(0, freq_idx - window)
            end_idx = min(len(magnitude), freq_idx + window + 1)
            
            local_magnitude = np.max(magnitude[start_idx:end_idx])
            
            if local_magnitude > threshold and local_magnitude > best_magnitude:
                best_magnitude = local_magnitude
                best_freq = target_freq
        
        return best_freq
    
    def detect_sequence(self, signal: np.ndarray, min_tone_duration: float = 0.1) -> List[str]:
        """
        Detect sequence of DTMF tones in a longer signal
        
        Args:
            signal: Audio signal containing multiple tones
            min_tone_duration: Minimum duration for a valid tone
            
        Returns:
            List of detected keys
        """
        detected_keys = []
        hop_size = self.window_samples // 2
        min_tone_samples = int(self.sample_rate * min_tone_duration)
        
        current_key = None
        key_start = 0
        
        for i in range(0, len(signal) - self.window_samples, hop_size):
            window = signal[i:i + self.window_samples]
            detected_key = self.detect_tone(window)
            
            if detected_key != current_key:
                # Key changed
                if current_key and (i - key_start) >= min_tone_samples:
                    detected_keys.append(current_key)
                
                current_key = detected_key
                key_start = i
        
        # Check final key
        if current_key and (len(signal) - key_start) >= min_tone_samples:
            detected_keys.append(current_key)
        
        return detected_keys


def demonstrate_dtmf():
    """Demonstrate DTMF generation and detection"""
    print("DTMF Demonstration")
    print("=" * 50)
    
    # Generate some tones
    generator = DTMFGenerator()
    detector = DTMFDetector()
    
    test_sequence = "123*0#"
    print(f"Generating sequence: {test_sequence}")
    
    # Generate audio
    audio = generator.generate_sequence(test_sequence)
    
    # Detect tones
    detected = detector.detect_sequence(audio)
    print(f"Detected sequence: {''.join(detected)}")
    
    # Show frequency mapping
    print("\nDTMF Frequency Mapping:")
    print("Key | Low Freq | High Freq")
    print("----|----------|----------")
    for key in "123A456B789C*0#D":
        if key in DTMFGenerator.DTMF_FREQUENCIES:
            low, high = DTMFGenerator.DTMF_FREQUENCIES[key]
            print(f" {key}  |   {low}   |   {high}")


if __name__ == "__main__":
    demonstrate_dtmf()
