"""
Call Flow Simulation Engine

This module implements an Interactive Voice Response (IVR) system simulation
for educational purposes. It demonstrates how automated phone systems work
with branching logic based on user keypad inputs.
"""

import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import json


class ActionType(Enum):
    """Types of actions in a call flow"""
    PLAY_MESSAGE = "play_message"
    COLLECT_INPUT = "collect_input"
    TRANSFER = "transfer"
    HANGUP = "hangup"
    RECORD = "record"
    BRANCH = "branch"


@dataclass
class CallFlowStep:
    """Represents a single step in a call flow"""
    id: str
    action_type: ActionType
    message: Optional[str] = None
    max_digits: int = 1
    timeout: float = 5.0
    retries: int = 3
    next_step: Optional[str] = None
    branches: Optional[Dict[str, str]] = None  # input -> next_step_id
    metadata: Optional[Dict[str, Any]] = None


class CallSession:
    """Represents an active call session"""
    
    def __init__(self, session_id: str, phone_number: str = "555-0123"):
        self.session_id = session_id
        self.phone_number = phone_number
        self.start_time = time.time()
        self.current_step = None
        self.collected_inputs = []
        self.call_log = []
        self.variables = {}  # Store session variables
        
    def log_action(self, action: str, details: str = ""):
        """Log an action in the call session"""
        timestamp = time.time() - self.start_time
        self.call_log.append({
            'timestamp': timestamp,
            'action': action,
            'details': details,
            'step': self.current_step
        })
    
    def add_input(self, input_value: str):
        """Add collected input to session"""
        self.collected_inputs.append({
            'value': input_value,
            'timestamp': time.time() - self.start_time,
            'step': self.current_step
        })


class CallFlowEngine:
    """Engine for executing call flows"""
    
    def __init__(self):
        self.flows = {}  # flow_name -> steps
        self.active_sessions = {}  # session_id -> CallSession
        self.message_handler = None  # Function to handle message playback
        self.input_handler = None   # Function to handle input collection
        
    def register_flow(self, flow_name: str, steps: List[CallFlowStep]):
        """Register a call flow"""
        flow_dict = {step.id: step for step in steps}
        self.flows[flow_name] = flow_dict
        
    def set_message_handler(self, handler: Callable[[str], None]):
        """Set handler for playing messages"""
        self.message_handler = handler
        
    def set_input_handler(self, handler: Callable[[int, float], str]):
        """Set handler for collecting input (max_digits, timeout) -> input"""
        self.input_handler = handler
    
    def start_call(self, session_id: str, flow_name: str, 
                   phone_number: str = "555-0123") -> CallSession:
        """Start a new call session"""
        if flow_name not in self.flows:
            raise ValueError(f"Unknown flow: {flow_name}")
        
        session = CallSession(session_id, phone_number)
        self.active_sessions[session_id] = session
        
        session.log_action("CALL_START", f"Flow: {flow_name}")
        
        # Find first step (usually 'start' or first in list)
        flow_steps = self.flows[flow_name]
        first_step = flow_steps.get('start') or list(flow_steps.values())[0]
        
        self.execute_flow(session_id, flow_name, first_step.id)
        return session
    
    def execute_flow(self, session_id: str, flow_name: str, step_id: str):
        """Execute a call flow starting from a specific step"""
        session = self.active_sessions.get(session_id)
        if not session:
            raise ValueError(f"Unknown session: {session_id}")
        
        flow = self.flows.get(flow_name)
        if not flow:
            raise ValueError(f"Unknown flow: {flow_name}")
        
        current_step_id = step_id
        retry_count = 0
        
        while current_step_id:
            step = flow.get(current_step_id)
            if not step:
                session.log_action("ERROR", f"Unknown step: {current_step_id}")
                break
            
            session.current_step = current_step_id
            session.log_action("STEP_START", f"Executing: {step.action_type.value}")
            
            try:
                next_step_id = self._execute_step(session, step)
                
                if next_step_id == current_step_id and retry_count >= step.retries:
                    # Max retries reached
                    session.log_action("MAX_RETRIES", f"Step: {current_step_id}")
                    break
                elif next_step_id == current_step_id:
                    retry_count += 1
                else:
                    retry_count = 0
                    current_step_id = next_step_id
                    
            except Exception as e:
                session.log_action("ERROR", f"Step execution failed: {str(e)}")
                break
        
        session.log_action("CALL_END", "Flow completed")
    
    def _execute_step(self, session: CallSession, step: CallFlowStep) -> Optional[str]:
        """Execute a single step and return next step ID"""
        
        if step.action_type == ActionType.PLAY_MESSAGE:
            if self.message_handler and step.message:
                self.message_handler(step.message)
            session.log_action("PLAY_MESSAGE", step.message or "")
            return step.next_step
            
        elif step.action_type == ActionType.COLLECT_INPUT:
            if step.message and self.message_handler:
                self.message_handler(step.message)
            
            if self.input_handler:
                user_input = self.input_handler(step.max_digits, step.timeout)
                session.add_input(user_input)
                session.log_action("INPUT_COLLECTED", user_input)
                
                # Check branches
                if step.branches and user_input in step.branches:
                    return step.branches[user_input]
                elif step.branches and '*' in step.branches:  # Default branch
                    return step.branches['*']
            
            return step.next_step
            
        elif step.action_type == ActionType.BRANCH:
            # Conditional branching based on session variables
            if step.branches:
                for condition, next_step in step.branches.items():
                    if self._evaluate_condition(session, condition):
                        return next_step
            return step.next_step
            
        elif step.action_type == ActionType.RECORD:
            session.log_action("RECORD_START", step.message or "Recording...")
            # Simulate recording
            time.sleep(1)
            session.log_action("RECORD_END", "Recording completed")
            return step.next_step
            
        elif step.action_type == ActionType.TRANSFER:
            session.log_action("TRANSFER", step.message or "Transferring call")
            return None  # End this flow
            
        elif step.action_type == ActionType.HANGUP:
            session.log_action("HANGUP", step.message or "Call ended")
            return None
        
        return step.next_step
    
    def _evaluate_condition(self, session: CallSession, condition: str) -> bool:
        """Evaluate a simple condition (for educational purposes)"""
        # Simple condition evaluation - in real systems this would be more complex
        if condition == "always":
            return True
        elif condition.startswith("input_count>"):
            threshold = int(condition.split(">")[1])
            return len(session.collected_inputs) > threshold
        return False
    
    def get_session_summary(self, session_id: str) -> Dict:
        """Get summary of call session"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {}
        
        return {
            'session_id': session.session_id,
            'phone_number': session.phone_number,
            'duration': time.time() - session.start_time,
            'inputs_collected': len(session.collected_inputs),
            'steps_executed': len([log for log in session.call_log if log['action'] == 'STEP_START']),
            'call_log': session.call_log,
            'collected_inputs': session.collected_inputs
        }


def create_sample_flow() -> List[CallFlowStep]:
    """Create a sample IVR flow for demonstration"""
    return [
        CallFlowStep(
            id="start",
            action_type=ActionType.PLAY_MESSAGE,
            message="Welcome to the Educational Call System. This is a simulation.",
            next_step="main_menu"
        ),
        CallFlowStep(
            id="main_menu",
            action_type=ActionType.COLLECT_INPUT,
            message="Press 1 for Account Information, 2 for Technical Support, 3 for Billing, or 0 for Operator.",
            max_digits=1,
            timeout=10.0,
            branches={
                "1": "account_info",
                "2": "tech_support", 
                "3": "billing",
                "0": "operator",
                "*": "invalid_option"
            }
        ),
        CallFlowStep(
            id="account_info",
            action_type=ActionType.COLLECT_INPUT,
            message="Please enter your 4-digit account number followed by the pound key.",
            max_digits=5,  # 4 digits + #
            timeout=15.0,
            next_step="account_verified"
        ),
        CallFlowStep(
            id="account_verified",
            action_type=ActionType.PLAY_MESSAGE,
            message="Thank you. Your account has been verified. Transferring to account services.",
            next_step="transfer"
        ),
        CallFlowStep(
            id="tech_support",
            action_type=ActionType.PLAY_MESSAGE,
            message="Connecting you to technical support. Please hold.",
            next_step="transfer"
        ),
        CallFlowStep(
            id="billing",
            action_type=ActionType.RECORD,
            message="Please describe your billing question after the beep.",
            next_step="billing_complete"
        ),
        CallFlowStep(
            id="billing_complete",
            action_type=ActionType.PLAY_MESSAGE,
            message="Your message has been recorded. A billing specialist will call you back within 24 hours.",
            next_step="hangup"
        ),
        CallFlowStep(
            id="operator",
            action_type=ActionType.PLAY_MESSAGE,
            message="Connecting you to an operator. Please hold.",
            next_step="transfer"
        ),
        CallFlowStep(
            id="invalid_option",
            action_type=ActionType.PLAY_MESSAGE,
            message="Invalid option. Please try again.",
            next_step="main_menu"
        ),
        CallFlowStep(
            id="transfer",
            action_type=ActionType.TRANSFER,
            message="Transferring your call..."
        ),
        CallFlowStep(
            id="hangup",
            action_type=ActionType.HANGUP,
            message="Thank you for calling. Goodbye."
        )
    ]


if __name__ == "__main__":
    # Demonstration
    engine = CallFlowEngine()
    engine.register_flow("sample", create_sample_flow())
    
    def mock_message_handler(message):
        print(f"🔊 SYSTEM: {message}")
    
    def mock_input_handler(max_digits, timeout):
        return input(f"📞 Enter up to {max_digits} digits (timeout: {timeout}s): ")
    
    engine.set_message_handler(mock_message_handler)
    engine.set_input_handler(mock_input_handler)
    
    print("Call Flow Simulation Demo")
    print("=" * 40)
    
    session = engine.start_call("demo_001", "sample")
    summary = engine.get_session_summary("demo_001")
    
    print("\nCall Summary:")
    print(f"Duration: {summary['duration']:.1f} seconds")
    print(f"Inputs collected: {summary['inputs_collected']}")
    print(f"Steps executed: {summary['steps_executed']}")
