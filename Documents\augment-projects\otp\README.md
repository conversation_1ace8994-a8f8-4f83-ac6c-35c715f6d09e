# OTP Bot - Educational One-Time Password System

An educational implementation of One-Time Password (OTP) algorithms for learning purposes.

## Features

- **TOTP (Time-based OTP)**: RFC 6238 compliant implementation
- **HOTP (HMAC-based OTP)**: RFC 4226 compliant implementation
- **Interactive Bot Interface**: Command-line bot for generating and verifying OTPs
- **Educational Content**: Detailed explanations of how OTP algorithms work
- **QR Code Generation**: Generate QR codes for authenticator apps
- **Multiple Hash Algorithms**: Support for SHA1, SHA256, and SHA512

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the OTP bot:
```bash
python otp_bot.py
```

## Educational Purpose

This project is designed for educational purposes to understand:
- How OTP algorithms work
- The difference between TOTP and HOTP
- Security considerations in OTP implementation
- Integration with authenticator applications

## Security Notice

This is an educational implementation. For production use, consider using well-tested libraries and following security best practices.
