
# Call Simulation Bot - Educational Telephony System

An educational simulation of automated calling systems and DTMF (keypad) processing for learning purposes.

## Features

- **DTMF Tone Generation**: Generate dual-tone multi-frequency signals for keypad simulation
- **DTMF Detection**: Detect and decode keypad presses from audio signals
- **Call Flow Simulation**: Interactive Voice Response (IVR) system simulation
- **Audio Recording**: Simulate call recording and playback
- **Educational Interface**: Command-line bot for learning telephony concepts
- **Safe Local Testing**: No actual phone calls - all simulation runs locally

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Quick Start

1. **Test the system:**
   ```bash
   python test_system.py
   ```

2. **Run the interactive demo:**
   ```bash
   python call_bot.py demo
   ```

3. **Make a specific call:**
   ```bash
   python call_bot.py call --phone 555-1234 --flow customer_service
   ```

### Available Commands

- `python call_bot.py demo` - Interactive demonstration
- `python call_bot.py call` - Make a simulated call
- `python call_bot.py flows` - List available call flows
- `python call_bot.py history` - Show call history
- `python call_bot.py summary <call_id>` - Show detailed call summary

### Example Scripts

- `python examples/dtmf_demo.py` - DTMF tone demonstration
- `python examples/custom_flow_example.py` - Custom call flow examples

## Educational Purpose

This project is designed for educational purposes to understand:
- How DTMF (keypad) tones work in telephony
- Interactive Voice Response (IVR) system design
- Audio signal processing for telecommunications
- Call flow logic and branching
- Telephony system architecture

## Important Notice

This is a LOCAL SIMULATION for educational purposes only. No actual phone calls are made.
All audio processing happens locally on your computer for learning about telephony concepts.

